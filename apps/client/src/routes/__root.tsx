import { useEffect } from "react";
import type { QueryClient } from "@tanstack/react-query";
import { createRootRouteWithContext, Outlet, useRouter } from "@tanstack/react-router";
import { useAuth, type AuthContext } from "@/context/auth";

type RootContext = {
    auth: AuthContext;
    queryClient: QueryClient;
};

export const Route = createRootRouteWithContext<RootContext>()({
    component: RootComponent,
});

function RootComponent() {
    const auth = useAuth();
    const router = useRouter();

    useEffect(() => {
        if (auth.isAuthenticated) {
            router.navigate({ to: "/" });
        } else {
            router.navigate({ to: "/login" });
        }
    }, [auth.isAuthenticated, router]);

    return <Outlet />;
}
