import { TextArea as RadixTextArea } from "@radix-ui/themes";
import type { TextAreaProps } from "@radix-ui/themes";
import { Controller } from "react-hook-form";
import type { FieldValues, Path } from "react-hook-form";
import { useFormContext } from "./formContext";
import { FormField } from "./formField";

type TextareaFieldProps<T extends FieldValues> = {
    name: Path<T>;
    label: string;
} & TextAreaProps;

export function TextArea<T extends FieldValues>({ name, label, ...rest }: TextareaFieldProps<T>) {
    const { control } = useFormContext();
    return (
        <Controller
            name={name}
            control={control}
            render={({ field, fieldState }) => (
                <FormField label={label} error={fieldState.error?.message}>
                    <RadixTextArea {...rest} {...field} />
                </FormField>
            )}
        />
    );
}
