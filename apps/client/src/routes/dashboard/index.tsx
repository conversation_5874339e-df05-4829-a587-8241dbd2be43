import { createFileRoute, redirect, useRouter } from "@tanstack/react-router";
import { Button } from "@/components";
import { useAuth } from "@/context/auth";

export const Route = createFileRoute("/dashboard/")({
    beforeLoad: ({ context, location }) => {
        if (!context.auth.isAuthenticated) {
            throw redirect({
                to: "/login",
                search: {
                    redirect: location.href,
                },
            });
        }
    },
    component: RouteComponent,
});

function RouteComponent() {
    const auth = useAuth();
    const router = useRouter();

    const handleLogout = () => {
        auth.logout();
        router.invalidate();
    };
    return (
        <div>
            Hello "/dashboard/"! <Button onClick={handleLogout}>Logout</Button>
        </div>
    );
}
