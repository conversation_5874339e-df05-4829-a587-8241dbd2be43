const bizzuDomain = (() => {
  if (import.meta.env.DEV) {
    return import.meta.env.VITE_DOMAIN;
  }

  const { host } = window.location;
  const parts = host.split(".");

  if (parts.length >= 3) {
    return `${parts[parts.length - 2]}.${parts[parts.length - 1]}`;
  }

  return host;
})();

export function getInvitationCode() {
  const searchParams = new URLSearchParams(window.location.search);

  return searchParams.get("invitationCode");
}

export function getNavigationLink(subdomain: string) {
  if (import.meta.env.DEV) {
    return `http://${bizzuDomain}:5174`;
  }

  return `https://${subdomain}.${bizzuDomain}`;
}

function setAccessTokenCookie({
  token,
  domain,
}: {
  token: string;
  domain: string;
}) {
  document.cookie = `access-token=${token}; domain=.${domain}; path=/; secure; samesite=lax; max-age=3600`;
}

export function setAuthCookie(accessToken: string) {
  setAccessTokenCookie({ domain: bizzuDomain, token: accessToken });
}

export function validateSubdomain(subdomain: string) {
  return /^[a-z0-9]+(?:-[a-z0-9]+)*$/.test(subdomain);
}
