import { supabase } from "./supabase";

const errors = {
  subdomain: "<PERSON><PERSON> jest ju<PERSON>, proszę wybrać inną nazwę",
  invitation: "<PERSON><PERSON> dostępu jest nieaktualny",
  email_address_invalid: "Podany email jest niepoprawny",
  email_not_confirmed: "Email nie został potwierdzony",
  user_already_exists: "Użytkownik juz istnieje",
  weak_password: "<PERSON>ło jest zbyt słabe. Wymagane jest minimum 6 znaków",
  registerError: "Błąd rejestracji",
};

type RegisterDomainPayload = {
  password: string;
  email: string;
  subdomain: string;
  invitationCode: string;
  companyName: string;
};

export async function registerDomain({
  password,
  email,
  subdomain,
  invitationCode,
  companyName,
}: RegisterDomainPayload): Promise<string> {
  const { data: invitationCodeResult, error: invitationCodeError } =
    await checkInvitation(invitationCode);

  if (!invitationCodeResult?.valid || invitationCodeError) {
    return Promise.reject(new Error(errors.invitation));
  }

  const { data: subdomainResult, error: subDomainError } =
    await checkSubdomainAvailability(subdomain);

  if (!subdomainResult?.available || subDomainError) {
    return Promise.reject(new Error(errors.subdomain));
  }

  const { error: signupError, data: signupData } = await signup({
    password,
    email,
    subdomain,
    invitationCode,
    companyName,
  });

  if (signupError) {
    const signupMessage =
      signupError.code && errors[signupError.code as keyof typeof errors];

    return Promise.reject(new Error(signupMessage || errors.registerError));
  }

  const accessToken = signupData.session?.access_token;

  if (!accessToken) {
    return Promise.reject(new Error(errors.registerError));
  }

  return Promise.resolve(accessToken);
}

function checkSubdomainAvailability(subdomain: string) {
  return supabase.rpc("check_subdomain_availability", {
    subdomain,
  });
}

function checkInvitation(invitation: string) {
  return supabase.rpc("validate_invitation_code", {
    invitation_code: invitation,
  });
}

function signup({
  password,
  email,
  subdomain,
  invitationCode,
  companyName,
}: Pick<
  RegisterDomainPayload,
  "password" | "email" | "subdomain" | "invitationCode" | "companyName"
>) {
  return supabase.auth.signUp({
    email,
    password,
    options: {
      data: {
        domainCreation: {
          subdomain,
          invitationCode,
          companyName,
        },
      },
    },
  });
}
