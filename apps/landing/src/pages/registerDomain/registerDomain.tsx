import { registerDomain } from "../../api/registerDomain";
import styles from "./registerDomain.module.scss";
import { useState } from "react";
import {
  getInvitationCode,
  getNavigationLink,
  setAuthCookie,
  validateSubdomain,
} from "./utils";
import { PrimaryButton } from "../../components/button/button";

export default function RegisterDomain() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [companyName, setCompanyName] = useState("");
  const [subdomain, setSubdomain] = useState("");
  const [formMessage, setFormMessage] = useState<string | null>();
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormMessage(null);
    setIsLoading(true);

    try {
      if (!validateSubdomain(subdomain)) {
        setFormMessage(
          "Nazwa domeny jest niepoprawna. Nazwa może składać się z małych liter, cyfr i myślników.",
        );
        return;
      }

      const accessToken = await registerDomain({
        password,
        email,
        subdomain,
        invitationCode: getInvitationCode() || "",
        companyName,
      });

      setFormMessage(
        "Domena została zarejestrowana. Za chwilę zostaniesz przekierowany do Twojej aplikacji",
      );

      setAuthCookie(accessToken);

      setTimeout(() => {
        window.location.href = getNavigationLink(subdomain);
      }, 600);
    } catch (e) {
      setFormMessage((e as Error).toString());
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={styles.pageContainer}>
      <div className={styles.pageContent}>
        <h2>Utworz Twoją nową domenę</h2>
        <p>
          Podane dane użytkownika posłużą do zalogowania do sytemu jako
          administrator
        </p>
        <form onSubmit={handleSubmit}>
          <div>
            <label htmlFor="email">Email</label>
            <input
              onChange={(e) => setEmail(e.target.value)}
              required
              type="email"
              name="email"
              id="email"
            />
          </div>
          <div>
            <label htmlFor="password">Hasło</label>
            <input
              onChange={(e) => setPassword(e.target.value)}
              required
              type="password"
              name="password"
              id="password"
              placeholder="Minimum 6 znaków"
            />
          </div>
          <div>
            <label htmlFor="companyName">Nazwa firmy</label>
            <input
              onChange={(e) => setCompanyName(e.target.value)}
              required
              type="text"
              name="companyName"
              id="companyName"
            />
          </div>
          <div>
            <label htmlFor="subdomain">Nazwa domeny</label>
            <input
              onChange={(e) => setSubdomain(e.target.value)}
              required
              type="text"
              name="subdomain"
              id="subdomain"
            />
          </div>
          {formMessage && <div>{formMessage}</div>}
          {isLoading && <div>Wysyłanie...</div>}
          <PrimaryButton
            disabled={isLoading}
            type="submit"
            className={styles.button}
          >
            Zarejestruj
          </PrimaryButton>
        </form>
      </div>
    </div>
  );
}
