name: Deploy Client to Development

on:
  push:
    branches: [develop]
    paths:
      - "apps/client/**"

jobs:
  deploy-client-dev:
    runs-on: ubuntu-latest
    environment: development

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "22"
          cache: "npm"
          cache-dependency-path: "apps/client/package-lock.json"

      - name: Install dependencies
        working-directory: ./apps/client
        run: npm ci

      - name: Run ESLint
        working-directory: ./apps/client
        run: npm run lint

      - name: Build
        working-directory: ./apps/client
        run: npm run build

      - name: Install Firebase CLI
        run: npm install -g firebase-tools

      - name: Authenticate with GCP
        uses: google-github-actions/auth@v2
        with:
          credentials_json: "${{ secrets.GCP_SA_KEY_DEV }}"
          export_environment_variables: true

      - name: Deploy to Firebase
        working-directory: ./apps/client
        run: firebase deploy --project bizzu-dev --only hosting:bizzu-client-dev
