import type React from "react";
import { ExclamationTriangleIcon } from "@radix-ui/react-icons";
import { Card, Flex, Text } from "@radix-ui/themes";
import styles from "./notification.module.scss";

export default function Notification({ children }: { children: React.ReactNode }) {
    return (
        <Card className={styles.container}>
            <Flex gap={"3"} align="center">
                <ExclamationTriangleIcon />
                <Text size="2">{children}</Text>
            </Flex>
        </Card>
    );
}
