import { Flex, Checkbox as Radix<PERSON><PERSON>ck<PERSON>, Text } from "@radix-ui/themes";
import { Controller } from "react-hook-form";
import type { FieldValues, Path } from "react-hook-form";
import { useFormContext } from "./formContext";
import { FormField } from "./formField";

type CheckboxFieldProps<T extends FieldValues> = {
    name: Path<T>;
    label: string;
};

export function Checkbox<T extends FieldValues>({ name, label }: CheckboxFieldProps<T>) {
    const { control } = useFormContext();
    return (
        <Controller
            name={name}
            control={control}
            render={({ field, fieldState }) => (
                <FormField label="" error={fieldState.error?.message}>
                    <Flex align="center" gap="2">
                        <RadixCheckbox checked={field.value} onCheckedChange={field.onChange} id={name} />
                        <Text as="label" htmlFor={name}>
                            {label}
                        </Text>
                    </Flex>
                </FormField>
            )}
        />
    );
}
