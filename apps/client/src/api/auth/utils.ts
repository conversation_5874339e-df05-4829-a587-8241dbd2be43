const bizzuDomain = import.meta.env.DEV ? "localhost" : "bizzu.app";

function getCookie(name: string) {
    const cookies = document.cookie.split(";").map((cookie) => cookie.trim());

    return cookies.find((cookie) => cookie.startsWith(`${name}=`))?.split("=")[1];
}

export function getAccessToken() {
    return getCookie("access-token");
}

export function removeAccessToken() {
    document.cookie = `access-token=; domain=.${bizzuDomain}; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT`;
}
