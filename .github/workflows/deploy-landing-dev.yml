name: Deploy Landing to Development

on:
  push:
    branches: [develop]
    paths:
      - "apps/landing/**"

jobs:
  deploy-landing-dev:
    runs-on: ubuntu-latest
    environment: development

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "22"
          cache: "npm"
          cache-dependency-path: "apps/landing/package-lock.json"

      - name: Install dependencies
        working-directory: ./apps/landing
        run: npm ci

      - name: Run ESLint
        working-directory: ./apps/landing
        run: npm run lint

      - name: Build
        working-directory: ./apps/landing
        run: npm run build

      - name: Install Firebase CLI
        run: npm install -g firebase-tools

      - name: Authenticate with GCP
        uses: google-github-actions/auth@v2
        with:
          credentials_json: "${{ secrets.GCP_SA_KEY_DEV }}"
          export_environment_variables: true

      - name: Deploy to Firebase
        working-directory: ./apps/landing
        run: firebase deploy --project bizzu-dev --only hosting:bizzu-dev
