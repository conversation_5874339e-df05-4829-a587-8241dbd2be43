import { createRouter, RouterProvider } from "@tanstack/react-router";
import { useAuth } from "@/context/auth";
import { queryClient } from "@/utils/queryClient";
import { routeTree } from "./routeTree.gen";

const router = createRouter({
    routeTree,
    context: {
        queryClient,
        auth: undefined!, // it will be defined in __root.tsx
    },
    defaultPreload: "intent",
    // Since we're using React Query, we don't want loader calls to ever be stale
    // This will ensure that the loader is always called when the route is preloaded or visited
    defaultPreloadStaleTime: 0,
    scrollRestoration: true,
});

declare module "@tanstack/react-router" {
    interface Register {
        router: typeof router;
    }
}

export default function App() {
    const auth = useAuth();

    return <RouterProvider router={router} context={{ auth }} />;
}
