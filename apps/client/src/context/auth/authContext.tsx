import * as React from "react";
import { useGetUser, useSignOut } from "@/api/auth";
import { ReactAuthContext } from "./context";

export function AuthProvider({ children }: { children: React.ReactNode }) {
    const { mutateAsync: logout } = useSignOut();
    const { data: user = null } = useGetUser();
    const isAuthenticated = !!user;

    return <ReactAuthContext.Provider value={{ isAuthenticated, user, logout }}>{children}</ReactAuthContext.Provider>;
}
