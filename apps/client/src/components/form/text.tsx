import { TextField } from "@radix-ui/themes";
import { Controller } from "react-hook-form";
import type { FieldValues, Path } from "react-hook-form";
import { useFormContext } from "./formContext";
import { FormField } from "./formField";

type InputFieldProps<T extends FieldValues> = {
    name: Path<T>;
    label: string;
} & TextField.RootProps;

export function Text<T extends FieldValues>({ name, label, ...rest }: InputFieldProps<T>) {
    const { control, errors } = useFormContext<T>();

    return (
        <Controller
            name={name}
            control={control}
            render={({ field }) => (
                <FormField label={label} error={errors[name]?.message}>
                    <TextField.Root {...rest} {...field} />
                </FormField>
            )}
        />
    );
}
