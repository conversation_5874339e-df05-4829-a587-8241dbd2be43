import type { ReactNode } from "react";
import { Flex, Text } from "@radix-ui/themes";

type FormFieldProps = {
    label: string;
    error?: string;
    children: ReactNode;
};

export const FormField = ({ label, error, children }: FormFieldProps) => (
    <Flex direction="column" gap="1">
        <Text as="label" weight="bold">
            {label}
        </Text>
        {children}
        {error && (
            <Text color="red" size="1">
                {error}
            </Text>
        )}
    </Flex>
);
