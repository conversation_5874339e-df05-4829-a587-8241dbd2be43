import type { AuthError as AuthErrorType, User } from "@supabase/supabase-js";
import { AuthError } from "@supabase/supabase-js";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import type { UseMutationOptions } from "@tanstack/react-query";
import { getSubdomain } from "@/utils/getSubdomain";
import supabase from "@/utils/supabase";
import type { AuthResponse, SignInPayload } from "./types";
import { getAccessToken, removeAccessToken } from "./utils";

const queryKeys = {
    getUser: ["auth/get/user"],
};

async function signIn(payload: SignInPayload): Promise<AuthResponse> {
    const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
        email: payload.email,
        password: payload.password,
    });

    if (signInError) {
        return Promise.reject(signInError);
    }

    const { data: rpcData } = await supabase.rpc("user_exists_in_workgroup", {
        user_id: signInData.user.id,
        subdomain: getSubdomain(),
    });

    if (!rpcData.allow) {
        await supabase.auth.signOut();
        return Promise.reject(new AuthError("Nie możesz zalogować sie do tej aplikacji"));
    }

    return signInData;
}

async function getUserFromCookie(): Promise<User | null> {
    const accessToken = getAccessToken();

    if (!accessToken) {
        return null;
    }

    const {
        data: { user },
    } = await supabase.auth.getUser(accessToken);

    if (user) {
        removeAccessToken();
    }

    return Promise.resolve(user);
}

async function getUser() {
    let user: User | null = null;

    const { data } = await supabase.auth.getUser();

    console.log(data.user);
    if (data.user) {
        user = data.user;
    } else {
        user = await getUserFromCookie();
    }

    if (!user) {
        return Promise.reject();
    }

    const { data: rpcData } = await supabase.rpc("user_exists_in_workgroup", {
        user_id: user.id,
        subdomain: getSubdomain(),
    });

    if (!rpcData.allow) {
        return Promise.reject();
    }

    return user;
}

export function useSignIn(options?: UseMutationOptions<AuthResponse, AuthErrorType, SignInPayload>) {
    const queryClient = useQueryClient();

    return useMutation({
        ...options,
        mutationFn: signIn,
        onSuccess(data, variables, context) {
            queryClient.setQueryData(queryKeys.getUser, data);
            queryClient.invalidateQueries({ queryKey: queryKeys.getUser });

            options?.onSuccess?.(data, variables, context);
        },
    });
}

export function useSignOut(options?: UseMutationOptions<unknown, AuthErrorType, void>) {
    const queryClient = useQueryClient();
    return useMutation({
        ...options,
        mutationFn: () => supabase.auth.signOut(),
        onSuccess(data, variables, context) {
            queryClient.setQueryData(queryKeys.getUser, null);
            queryClient.invalidateQueries({ queryKey: queryKeys.getUser });

            options?.onSuccess?.(data, variables, context);
        },
    });
}

export function useGetUser() {
    return useQuery({
        queryKey: queryKeys.getUser,
        queryFn: () => getUser(),
        select(user) {
            return user;
        },
    });
}
