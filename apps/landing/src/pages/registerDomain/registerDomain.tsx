import { registerDomain } from "../../api/registerDomain";
import styles from "./registerDomain.module.scss";
import { useState } from "react";
import {
  getInvitationCode,
  getNavigationLink,
  setAuthCookie,
  validateSubdomain,
} from "./utils";
import { PrimaryButton } from "../../components/button/button";

export default function RegisterDomain() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [companyName, setCompanyName] = useState("");
  const [subdomain, setSubdomain] = useState("");
  const [formMessage, setFormMessage] = useState<string | null>();
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormMessage(null);
    setIsLoading(true);

    try {
      if (!validateSubdomain(subdomain)) {
        setFormMessage(
          "Nazwa konta jest niepoprawna. Nazwa może składać się z małych liter, cyfr i myślników.",
        );
        return;
      }

      const accessToken = await registerDomain({
        password,
        email,
        subdomain,
        invitationCode: getInvitationCode() || "",
        companyName,
      });

      setFormMessage(
        "Konto zostało utworzone. Za chwilę zostaniesz przekierowany do Twojej aplikacji",
      );

      setAuthCookie(accessToken);

      setTimeout(() => {
        window.location.href = getNavigationLink(subdomain);
      }, 600);
    } catch (e) {
      setFormMessage((e as Error).toString());
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={styles.pageContainer}>
      <div className={styles.pageContent}>
        <h2>Utwórz Twoje nowe konto</h2>
        <p>
          Wypełnij poniższe dane, aby utworzyć konto administratora i
          skonfigurować Twoją firmę w systemie Bizzu.
        </p>
        <form onSubmit={handleSubmit}>
          <div className={styles.formSection}>
            <h3 className={styles.sectionTitle}>Dane użytkownika</h3>
            <p className={styles.sectionDescription}>
              Te dane posłużą do zalogowania do systemu jako administrator
            </p>
            <div>
              <label htmlFor="email">Email / Login</label>
              <input
                onChange={(e) => setEmail(e.target.value)}
                required
                type="email"
                name="email"
                id="email"
                placeholder="<EMAIL>"
              />
            </div>
            <div>
              <label htmlFor="password">Hasło</label>
              <input
                onChange={(e) => setPassword(e.target.value)}
                required
                type="password"
                name="password"
                id="password"
                placeholder="Minimum 6 znaków"
              />
            </div>
          </div>

          <div className={styles.formSection}>
            <h3 className={styles.sectionTitle}>Dane firmy</h3>
            <p className={styles.sectionDescription}>
              Skonfiguruj nazwę firmy i adres Twojej aplikacji
            </p>
            <div>
              <label htmlFor="companyName">Nazwa firmy</label>
              <input
                onChange={(e) => setCompanyName(e.target.value)}
                required
                type="text"
                name="companyName"
                id="companyName"
                placeholder="Nazwa Twojej firmy"
              />
            </div>
            <div>
              <label htmlFor="subdomain">Nazwa konta / Subdomena</label>
              <input
                onChange={(e) => setSubdomain(e.target.value)}
                required
                type="text"
                name="subdomain"
                id="subdomain"
                placeholder="nazwa-firmy"
              />
              <div className={styles.subdomainHint}>
                Twoja aplikacja będzie dostępna pod adresem:{" "}
                <strong>{subdomain || "nazwa-konta"}.bizzu.app</strong>
              </div>
            </div>
          </div>

          {formMessage && (
            <div className={styles.formMessage}>{formMessage}</div>
          )}
          {isLoading && (
            <div className={styles.loadingMessage}>Wysyłanie...</div>
          )}
          <PrimaryButton
            disabled={isLoading}
            type="submit"
            className={styles.button}
          >
            Utwórz konto
          </PrimaryButton>
        </form>
      </div>
    </div>
  );
}
