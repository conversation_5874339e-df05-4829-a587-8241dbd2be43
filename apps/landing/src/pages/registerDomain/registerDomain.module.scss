.pageContainer {
  min-height: 50vh;
  background-color: #fff;

  input {
    background-color: white;
    color: black;
    width: 100%;
    padding: 4px;
  }

  div {
    margin: 8px auto;
  }

  label {
    font-size: 13px;
  }
}

.pageContent {
  max-width: 900px;
  margin: 0 auto;
  padding: 2rem;
}

.button {
  margin-top: 24px;
  font-size: 13px;
}

.formSection {
  margin: 32px 0;
  padding: 24px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #fafafa;

  &:first-of-type {
    margin-top: 24px;
  }
}

.sectionTitle {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.sectionDescription {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: #666;
}

.subdomainHint {
  margin-top: 8px;
  font-size: 12px;
  color: #666;
  padding: 8px;
  background-color: #f0f8ff;
  border-radius: 4px;
  border-left: 3px solid #007acc;
}

.formMessage {
  margin: 16px 0;
  padding: 12px;
  border-radius: 4px;
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.loadingMessage {
  margin: 16px 0;
  padding: 12px;
  border-radius: 4px;
  background-color: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}
