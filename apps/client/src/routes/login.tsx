import { createFileRoute, redirect } from "@tanstack/react-router";
import { z } from "zod/v4";
import { useSignIn } from "@/api/auth/auth";
import { Box, Container, Heading } from "@/components";
import LoginForm from "@/features/loginForm/loginForm";
import type { LoginForm as LoginFormType } from "@/features/loginForm/loginForm";

export const Route = createFileRoute("/login")({
    validateSearch: z.object({
        redirect: z.string().optional().catch(""),
    }),
    beforeLoad: ({ context, search }) => {
        if (context.auth.isAuthenticated) {
            throw redirect({ to: search.redirect || "/dashboard" });
        }
    },
    component: LoginPage,
});

function LoginPage() {
    const { mutate, error } = useSignIn();

    const handleLogin = async (data: LoginFormType) => {
        mutate(data);
    };

    return (
        <Container
            size="1"
            style={{ height: "100vh", display: "flex", justifyContent: "center", alignItems: "center" }}
        >
            <Box
                style={{
                    width: "100%",
                    maxWidth: "400px",
                    padding: "2rem",
                    boxShadow: "0 0 10px rgba(0,0,0,0.1)",
                    borderRadius: "8px",
                    backgroundColor: "white",
                }}
            >
                <Heading as="h2" size="4" mb="4" align="center">
                    Login
                </Heading>
                <LoginForm onLogin={handleLogin} error={error?.message} />
            </Box>
        </Container>
    );
}
