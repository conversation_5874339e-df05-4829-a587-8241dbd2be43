@import url("https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100..900;1,100..900&display=swap");

body,
html {
    margin: 0;
    padding: 0;
}

.radix-themes {
    --default-font-family: "Roboto", sans-serif;
}

/* generated with https://www.radix-ui.com/colors/custom */
:root,
.light,
.light-theme {
    --crimson-1: #fffbff;
    --crimson-2: #fef6ff;
    --crimson-3: #ffe7ff;
    --crimson-4: #fed9ff;
    --crimson-5: #fac9ff;
    --crimson-6: #f4b8fe;
    --crimson-7: #eca0f8;
    --crimson-8: #e17ef2;
    --crimson-9: #e100ff;
    --crimson-10: #d300f1;
    --crimson-11: #ae00c7;
    --crimson-12: #5f006f;

    --crimson-a1: #ff00ff04;
    --crimson-a2: #e300ff09;
    --crimson-a3: #ff00ff18;
    --crimson-a4: #f900ff26;
    --crimson-a5: #e800ff36;
    --crimson-a6: #d800fc47;
    --crimson-a7: #cc00ed5f;
    --crimson-a8: #c400e681;
    --crimson-a9: #e100ff;
    --crimson-a10: #d300f1;
    --crimson-a11: #ae00c7;
    --crimson-a12: #5f006f;

    --crimson-contrast: #fff;
    --crimson-surface: #fef4ffcc;
    --crimson-indicator: #e100ff;
    --crimson-track: #e100ff;
}

@supports (color: color(display-p3 1 1 1)) {
    @media (color-gamut: p3) {
        :root,
        .light,
        .light-theme {
            --crimson-1: oklch(99.3% 0.0074 321.6);
            --crimson-2: oklch(98.3% 0.015 321.6);
            --crimson-3: oklch(95.6% 0.0446 321.6);
            --crimson-4: oklch(92.8% 0.07 321.6);
            --crimson-5: oklch(89.7% 0.0919 321.6);
            --crimson-6: oklch(85.9% 0.1151 321.6);
            --crimson-7: oklch(80.9% 0.1449 321.6);
            --crimson-8: oklch(74.4% 0.1889 321.6);
            --crimson-9: oklch(65.7% 0.3128 321.6);
            --crimson-10: oklch(61.8% 0.3128 321.6);
            --crimson-11: oklch(53.1% 0.294 321.6);
            --crimson-12: oklch(34.1% 0.2045 321.6);

            --crimson-a1: color(display-p3 0.7569 0.0235 1 / 0.016);
            --crimson-a2: color(display-p3 0.7843 0.0235 0.8941 / 0.036);
            --crimson-a3: color(display-p3 0.8314 0.0118 0.9176 / 0.091);
            --crimson-a4: color(display-p3 0.8157 0.0078 0.9216 / 0.146);
            --crimson-a5: color(display-p3 0.7529 0.0039 0.9451 / 0.204);
            --crimson-a6: color(display-p3 0.7137 0.0039 0.9294 / 0.271);
            --crimson-a7: color(display-p3 0.6745 0.0039 0.8706 / 0.361);
            --crimson-a8: color(display-p3 0.6549 0.0039 0.8471 / 0.487);
            --crimson-a9: color(display-p3 0.7686 0 0.9569 / 0.828);
            --crimson-a10: color(display-p3 0.7098 0 0.898 / 0.844);
            --crimson-a11: color(display-p3 0.5725 0 0.7176 / 0.879);
            --crimson-a12: color(display-p3 0.3059 0 0.3922 / 0.953);

            --crimson-contrast: #fff;
            --crimson-surface: color(display-p3 0.9922 0.9608 1 / 0.8);
            --crimson-indicator: oklch(65.7% 0.3128 321.6);
            --crimson-track: oklch(65.7% 0.3128 321.6);
        }
    }
}

:root,
.light,
.light-theme {
    --gray-1: #fefbfd;
    --gray-2: #fdf8fc;
    --gray-3: #f6edf4;
    --gray-4: #f0e4ed;
    --gray-5: #ebdbe7;
    --gray-6: #e5d3e1;
    --gray-7: #ddc7d8;
    --gray-8: #ceb1c8;
    --gray-9: #a08499;
    --gray-10: #947a8e;
    --gray-11: #725d6d;
    --gray-12: #2a1b26;

    --gray-a1: #c0008004;
    --gray-a2: #b7009207;
    --gray-a3: #80006412;
    --gray-a4: #7200551b;
    --gray-a5: #72005524;
    --gray-a6: #6900522c;
    --gray-a7: #65004e38;
    --gray-a8: #5f004c4e;
    --gray-a9: #3b012c7b;
    --gray-a10: #32002785;
    --gray-a11: #210019a2;
    --gray-a12: #11000ce4;

    --gray-contrast: #ffffff;
    --gray-surface: #ffffffcc;
    --gray-indicator: #a08499;
    --gray-track: #a08499;
}

@supports (color: color(display-p3 1 1 1)) {
    @media (color-gamut: p3) {
        :root,
        .light,
        .light-theme {
            --gray-1: oklch(99.2% 0.0042 334);
            --gray-2: oklch(98.3% 0.0076 334);
            --gray-3: oklch(95.6% 0.0138 334);
            --gray-4: oklch(93.2% 0.0182 334);
            --gray-5: oklch(90.9% 0.0235 334);
            --gray-6: oklch(88.6% 0.027 334);
            --gray-7: oklch(85.4% 0.0333 334);
            --gray-8: oklch(79.4% 0.045 334);
            --gray-9: oklch(64.6% 0.0454 334);
            --gray-10: oklch(61.1% 0.0432 334);
            --gray-11: oklch(50.5% 0.0373 334);
            --gray-12: oklch(24.5% 0.0313 334);

            --gray-a1: color(display-p3 0.5137 0.0235 0.5137 / 0.016);
            --gray-a2: color(display-p3 0.5804 0.0235 0.5804 / 0.028);
            --gray-a3: color(display-p3 0.3961 0.0078 0.3961 / 0.071);
            --gray-a4: color(display-p3 0.3725 0.0039 0.298 / 0.106);
            --gray-a5: color(display-p3 0.349 0.0078 0.2902 / 0.138);
            --gray-a6: color(display-p3 0.3294 0.0039 0.2824 / 0.169);
            --gray-a7: color(display-p3 0.3137 0.0039 0.2745 / 0.216);
            --gray-a8: color(display-p3 0.302 0.0039 0.2745 / 0.302);
            --gray-a9: color(display-p3 0.1804 0.0039 0.1569 / 0.479);
            --gray-a10: color(display-p3 0.1608 0 0.1373 / 0.518);
            --gray-a11: color(display-p3 0.1059 0 0.0863 / 0.632);
            --gray-a12: color(display-p3 0.0549 0 0.0392 / 0.891);

            --gray-contrast: #ffffff;
            --gray-surface: color(display-p3 1 1 1 / 80%);
            --gray-indicator: oklch(64.6% 0.0454 334);
            --gray-track: oklch(64.6% 0.0454 334);
        }
    }
}

.dark,
.dark-theme {
    --crimson-1: #170d19;
    --crimson-2: #220f26;
    --crimson-3: #3a0e42;
    --crimson-4: #4d0259;
    --crimson-5: #5a0868;
    --crimson-6: #681a76;
    --crimson-7: #802990;
    --crimson-8: #a336b7;
    --crimson-9: #d000ee;
    --crimson-10: #c200e0;
    --crimson-11: #f782ff;
    --crimson-12: #fccdff;

    --crimson-a1: #bc00f409;
    --crimson-a2: #ce00fa17;
    --crimson-a3: #d703fd35;
    --crimson-a4: #d600fd4e;
    --crimson-a5: #d700fd5e;
    --crimson-a6: #dd26fd6d;
    --crimson-a7: #e03efe89;
    --crimson-a8: #e346ffb2;
    --crimson-a9: #df00ffed;
    --crimson-a10: #dd00ffde;
    --crimson-a11: #f782ff;
    --crimson-a12: #fccdff;

    --crimson-contrast: #fff;
    --crimson-surface: #330e3b80;
    --crimson-indicator: #d000ee;
    --crimson-track: #d000ee;
}

@supports (color: color(display-p3 1 1 1)) {
    @media (color-gamut: p3) {
        .dark,
        .dark-theme {
            --crimson-1: oklch(17.8% 0.0284 321.1);
            --crimson-2: oklch(20.8% 0.0506 321.1);
            --crimson-3: oklch(26.5% 0.1005 321.1);
            --crimson-4: oklch(30.6% 0.1437 321.1);
            --crimson-5: oklch(34.3% 0.1551 321.1);
            --crimson-6: oklch(38.9% 0.1579 321.1);
            --crimson-7: oklch(45.6% 0.1731 321.1);
            --crimson-8: oklch(54.5% 0.2082 321.1);
            --crimson-9: oklch(62.1% 0.2963 321.1);
            --crimson-10: oklch(58% 0.2963 321.1);
            --crimson-11: oklch(78.6% 0.2508 321.1);
            --crimson-12: oklch(90.6% 0.0897 321.1);

            --crimson-a1: color(display-p3 0.7216 0 0.9843 / 0.03);
            --crimson-a2: color(display-p3 0.7137 0.0235 0.9882 / 0.085);
            --crimson-a3: color(display-p3 0.7804 0.0667 1 / 0.198);
            --crimson-a4: color(display-p3 0.7843 0 1 / 0.29);
            --crimson-a5: color(display-p3 0.8 0.0667 1 / 0.349);
            --crimson-a6: color(display-p3 0.8196 0.2118 1 / 0.408);
            --crimson-a7: color(display-p3 0.8392 0.298 1 / 0.513);
            --crimson-a8: color(display-p3 0.8471 0.3255 0.9961 / 0.673);
            --crimson-a9: color(display-p3 0.8275 0.1686 1 / 0.895);
            --crimson-a10: color(display-p3 0.8196 0.1569 1 / 0.837);
            --crimson-a11: color(display-p3 0.9373 0.549 1 / 0.971);
            --crimson-a12: color(display-p3 0.9725 0.8235 1 / 0.988);

            --crimson-contrast: #fff;
            --crimson-surface: color(display-p3 0.1725 0.0588 0.2118 / 0.5);
            --crimson-indicator: oklch(62.1% 0.2963 321.1);
            --crimson-track: oklch(62.1% 0.2963 321.1);
        }
    }
}

.dark,
.dark-theme {
    --gray-1: #111113;
    --gray-2: #19191b;
    --gray-3: #222325;
    --gray-4: #292a2e;
    --gray-5: #303136;
    --gray-6: #393a40;
    --gray-7: #46484f;
    --gray-8: #5f606a;
    --gray-9: #6c6e79;
    --gray-10: #797b86;
    --gray-11: #b2b3bd;
    --gray-12: #eeeef0;

    --gray-a1: #1111bb03;
    --gray-a2: #cbcbf90b;
    --gray-a3: #d6e2f916;
    --gray-a4: #d1d9f920;
    --gray-a5: #d7ddfd28;
    --gray-a6: #d9defc33;
    --gray-a7: #dae2fd43;
    --gray-a8: #e0e3fd60;
    --gray-a9: #e0e4fd70;
    --gray-a10: #e3e7fd7e;
    --gray-a11: #eff0feb9;
    --gray-a12: #fdfdffef;

    --gray-contrast: #ffffff;
    --gray-surface: rgba(0, 0, 0, 0.05);
    --gray-indicator: #6c6e79;
    --gray-track: #6c6e79;
}

@supports (color: color(display-p3 1 1 1)) {
    @media (color-gamut: p3) {
        .dark,
        .dark-theme {
            --gray-1: oklch(17.8% 0.0042 277.7);
            --gray-2: oklch(21.5% 0.004 277.7);
            --gray-3: oklch(25.5% 0.0055 277.7);
            --gray-4: oklch(28.4% 0.0075 277.7);
            --gray-5: oklch(31.4% 0.0089 277.7);
            --gray-6: oklch(35% 0.01 277.7);
            --gray-7: oklch(40.2% 0.0121 277.7);
            --gray-8: oklch(49.2% 0.0157 277.7);
            --gray-9: oklch(54% 0.0167 277.7);
            --gray-10: oklch(58.6% 0.0165 277.7);
            --gray-11: oklch(77% 0.0138 277.7);
            --gray-12: oklch(94.9% 0.0026 277.7);

            --gray-a1: color(display-p3 0.0667 0.0667 0.9412 / 0.009);
            --gray-a2: color(display-p3 0.8 0.8 0.9804 / 0.043);
            --gray-a3: color(display-p3 0.851 0.898 0.9882 / 0.085);
            --gray-a4: color(display-p3 0.8392 0.8706 1 / 0.122);
            --gray-a5: color(display-p3 0.8471 0.8745 1 / 0.156);
            --gray-a6: color(display-p3 0.8784 0.898 1 / 0.194);
            --gray-a7: color(display-p3 0.8745 0.9059 0.9961 / 0.257);
            --gray-a8: color(display-p3 0.8941 0.9059 1 / 0.37);
            --gray-a9: color(display-p3 0.8902 0.9098 1 / 0.433);
            --gray-a10: color(display-p3 0.902 0.9176 1 / 0.488);
            --gray-a11: color(display-p3 0.9451 0.949 1 / 0.719);
            --gray-a12: color(display-p3 0.9922 0.9922 1 / 0.937);

            --gray-contrast: #ffffff;
            --gray-surface: color(display-p3 0 0 0 / 5%);
            --gray-indicator: oklch(54% 0.0167 277.7);
            --gray-track: oklch(54% 0.0167 277.7);
        }
    }
}
