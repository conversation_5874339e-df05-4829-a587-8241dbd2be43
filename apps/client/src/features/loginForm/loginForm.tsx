import { useTranslation } from "react-i18next";
import { z } from "zod/v4";
import { Button, Flex } from "@/components";
import { Form, Text } from "@/components/form";
import { Notification } from "@/components/notification";

export type LoginForm = {
    email: string;
    password: string;
};

const schema: z.ZodSchema<LoginForm> = z.object({
    email: z.email(),
    password: z.string().min(2),
});

export default function LoginForm({ onLogin, error }: { onLogin: (data: LoginForm) => void; error?: string }) {
    const { t } = useTranslation();

    return (
        <Form schema={schema} onSubmit={onLogin} defaultValues={{ password: "", email: "" }}>
            <Flex direction="column" gap="4">
                {error && <Notification>{error}</Notification>}
                <Text<LoginForm> name="email" label="email" />
                <Text<LoginForm> name="password" label="Password" type="password" />

                <Button type="submit">{t("login")}</Button>
            </Flex>
        </Form>
    );
}
